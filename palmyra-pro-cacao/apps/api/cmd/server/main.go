package main

import (
	"context"
	"log"
	"net/http"
	"os"
	"palmyra-pro-api/pkg/api"
	"palmyra-pro-api/pkg/api/middlewares"
	"palmyra-pro-api/pkg/api/routes"
	"palmyra-pro-api/pkg/cors"
	"palmyra-pro-api/pkg/db"
	"palmyra-pro-api/pkg/db/repository"
	"palmyra-pro-api/pkg/utils"
	"strings"

	firebase "firebase.google.com/go/v4"
	"google.golang.org/api/option"
)

func main() {

	dbName := utils.GetEnvOrPanic("DB_NAME")
	dbHost := utils.GetEnvOrPanic("DB_HOST")
	dbPort := utils.GetEnvOrPanic("DB_PORT")
	dbUser := utils.GetEnvOrPanic("DB_USER")
	dbPassword := utils.GetEnvOrPanic("DB_PASSWORD")
	dbSSLMode := utils.GetEnvOrPanic("DB_SSL_MODE")

	allowedOrigin := os.Getenv("ALLOWED_ORIGIN")
	origins := strings.Split(allowedOrigin, ",")

	// Metabase config
	MetabaseURL := utils.GetEnvOrPanic("METABASE_SITE_URL")
	MetabasePrivateKey := utils.GetEnvOrPanic("METABASE_SECRET_KEY")

	// Authentication config
	connection, err := db.NewDB(&db.Config{
		Name:         dbName,
		Host:         dbHost,
		Port:         dbPort,
		User:         dbUser,
		Password:     dbPassword,
		SSLMode:      dbSSLMode,
		Debug:        false,
		MaxOpenConn:  0,
		MaxIdleConns: 0,
	})

	if err != nil {
		panic(err)
	}

	// 4. Init Firebase Admin SDK
	ctx := context.Background()

	// Try to get Firebase credentials from environment variable first
	firebaseCredentials := os.Getenv("FIREBASE_SERVICE_ACCOUNT_KEY")
	var firebaseApp *firebase.App

	if firebaseCredentials != "" {
		// Use credentials from environment variable
		firebaseApp, err = firebase.NewApp(ctx, nil, option.WithCredentialsJSON([]byte(firebaseCredentials)))
		if err != nil {
			log.Fatal("Error on init Firebase App with env credentials:", err)
		}
	} else {
		// Fallback to file-based credentials
		jwtKeyUrl := utils.GetEnvOrPanic("FIREBASE_ADMIN_KEY_PATH")
		firebaseApp, err = firebase.NewApp(ctx, nil, option.WithCredentialsFile(jwtKeyUrl))
		if err != nil {
			log.Fatal("Error on init Firebase App with file credentials:", err)
		}
	}
	authClient, err := firebaseApp.Auth(ctx)
	if err != nil {
		log.Fatal("Error on init Firebase Auth :", err)
	}

	// create a type that satisfies the `api.ServerInterface`, which contains an implementation of every operation from the generated code
	server := routes.Server{
		FarmerRepo:         repository.NewFarmerRepo(connection),
		UserRepo:           repository.NewUserRepo(connection),
		MetabasePrivateKey: MetabasePrivateKey,
		MetabaseURL:        MetabaseURL,
	}
	handler := api.NewStrictHandlerWithOptions(server, nil, api.NewErrorHandlerOptions())

	m := http.NewServeMux()

	// get an `http.Handler` that we can use
	h := api.HandlerFromMux(handler, m)

	// enable validations
	validations, err := middlewares.CreateValidationsMiddleware(authClient)
	if err != nil {
		log.Fatal(err)
	}
	h = validations(h)

	// Enable CORS
	h = cors.NewCorsMiddleware(origins).Handler(h)

	s := &http.Server{
		Handler: h,
		Addr:    "0.0.0.0:3000",
	}

	// And we serve HTTP until the world ends and maybe even later.
	err = s.ListenAndServe()
	if err != nil {
		log.Fatal(err)
	}
}
